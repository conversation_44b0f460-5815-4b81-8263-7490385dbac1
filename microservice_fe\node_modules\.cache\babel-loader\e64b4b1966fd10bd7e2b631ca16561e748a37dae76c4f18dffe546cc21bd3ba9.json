{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,TextField,FormControl,InputLabel,Select,MenuItem,Typography,Paper,Divider,Alert,IconButton}from'@mui/material';import CloseIcon from'@mui/icons-material/Close';import PaymentIcon from'@mui/icons-material/Payment';import ReceiptIcon from'@mui/icons-material/Receipt';import InfoOutlinedIcon from'@mui/icons-material/InfoOutlined';import{PaymentMethodMap}from'../../models';import{formatCurrency}from'../../utils/formatters';import{formatDateLocalized}from'../../utils/dateUtils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PaymentForm=_ref=>{let{open,contract,onClose,onSubmit,remainingAmount,loading=false}=_ref;const[paymentAmount,setPaymentAmount]=useState(0);const[paymentMethod,setPaymentMethod]=useState(0);// Default: Tiền mặt\nconst[note,setNote]=useState('');const[error,setError]=useState(null);useEffect(()=>{if(open&&contract){// Reset form when dialog opens\nsetPaymentAmount(remainingAmount);setPaymentMethod(0);setNote('');setError(null);}},[open,contract,remainingAmount]);const handleSubmit=e=>{// Prevent default form submission behavior\nif(e){e.preventDefault();}if(!contract||loading)return;// Validate payment amount\nif(!paymentAmount||paymentAmount<=0){setError('Số tiền thanh toán phải lớn hơn 0');return;}if(paymentAmount>remainingAmount){setError(\"S\\u1ED1 ti\\u1EC1n thanh to\\xE1n kh\\xF4ng \\u0111\\u01B0\\u1EE3c v\\u01B0\\u1EE3t qu\\xE1 s\\u1ED1 ti\\u1EC1n c\\xF2n l\\u1EA1i (\".concat(formatCurrency(remainingAmount),\")\"));return;}// Create payment object\nconst payment={paymentDate:new Date().toISOString(),paymentMethod,paymentAmount,note:note||undefined,customerContractId:contract.id,customerId:contract.customerId};onSubmit(payment);};const formatDate=dateString=>{if(!dateString)return'-';return formatDateLocalized(dateString);};if(!contract)return null;return/*#__PURE__*/_jsxs(Dialog,{open:open,onClose:onClose,maxWidth:\"md\",fullWidth:true,sx:{'& .MuiDialog-paper':{borderRadius:2,boxShadow:24}},children:[/*#__PURE__*/_jsxs(DialogTitle,{sx:{bgcolor:'primary.main',color:'primary.contrastText',display:'flex',justifyContent:'space-between',alignItems:'center',px:3,py:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(PaymentIcon,{}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Thanh to\\xE1n h\\u1EE3p \\u0111\\u1ED3ng\"})]}),/*#__PURE__*/_jsx(IconButton,{edge:\"end\",color:\"inherit\",onClick:onClose,\"aria-label\":\"close\",children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsxs(DialogContent,{sx:{p:3},children:[/*#__PURE__*/_jsxs(Paper,{variant:\"outlined\",sx:{p:2,mb:3},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",fontWeight:\"bold\",gutterBottom:true,sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(InfoOutlinedIcon,{fontSize:\"small\",color:\"primary\"}),\"Th\\xF4ng tin h\\u1EE3p \\u0111\\u1ED3ng\"]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:{xs:'1fr',sm:'1fr 1fr'},gap:2},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"M\\xE3 h\\u1EE3p \\u0111\\u1ED3ng\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",fontWeight:\"medium\",children:[\"#\",contract.id]})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Kh\\xE1ch h\\xE0ng\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",fontWeight:\"medium\",children:contract.customerName})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:formatDate(contract.startingDate)})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Ng\\xE0y k\\u1EBFt th\\xFAc\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:formatDate(contract.endingDate)})]})]}),/*#__PURE__*/_jsx(Divider,{sx:{my:2}}),/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:{xs:'1fr',sm:'1fr 1fr 1fr'},gap:2},children:[/*#__PURE__*/_jsxs(Paper,{variant:\"outlined\",sx:{p:1.5,bgcolor:'background.default',borderColor:'divider'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"T\\u1ED5ng gi\\xE1 tr\\u1ECB h\\u1EE3p \\u0111\\u1ED3ng\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:formatCurrency(contract.totalAmount)})]}),/*#__PURE__*/_jsxs(Paper,{variant:\"outlined\",sx:{p:1.5,bgcolor:'background.default',borderColor:'divider'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"\\u0110\\xE3 thanh to\\xE1n\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:formatCurrency(contract.totalPaid||0)})]}),/*#__PURE__*/_jsxs(Paper,{sx:{p:1.5,bgcolor:'primary.light',color:'primary.contrastText',borderRadius:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"inherit\",children:\"C\\xF2n l\\u1EA1i c\\u1EA7n thanh to\\xE1n\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"bold\",children:formatCurrency(remainingAmount)})]})]})]}),/*#__PURE__*/_jsxs(Paper,{variant:\"outlined\",sx:{p:2,mb:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",fontWeight:\"bold\",gutterBottom:true,sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(ReceiptIcon,{fontSize:\"small\",color:\"primary\"}),\"Th\\xF4ng tin thanh to\\xE1n\"]}),error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:error}),/*#__PURE__*/_jsxs(Box,{component:\"form\",onSubmit:handleSubmit,sx:{display:'grid',gridTemplateColumns:{xs:'1fr',sm:'1fr 1fr'},gap:3},children:[/*#__PURE__*/_jsx(TextField,{label:\"S\\u1ED1 ti\\u1EC1n thanh to\\xE1n\",type:\"number\",fullWidth:true,value:paymentAmount,onChange:e=>setPaymentAmount(Number(e.target.value)),onKeyDown:e=>{if(e.key==='Enter'){e.preventDefault();handleSubmit();}},sx:{'& .MuiInputBase-input':{paddingRight:'40px'},'& .MuiInputBase-root':{position:'relative','&::after':{content:'\"VND\"',position:'absolute',right:'14px',top:'50%',transform:'translateY(-50%)',color:'rgba(0, 0, 0, 0.54)',pointerEvents:'none'}}},error:!!error,required:true}),/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,required:true,children:[/*#__PURE__*/_jsx(InputLabel,{children:\"Ph\\u01B0\\u01A1ng th\\u1EE9c thanh to\\xE1n\"}),/*#__PURE__*/_jsx(Select,{value:paymentMethod,label:\"Ph\\u01B0\\u01A1ng th\\u1EE9c thanh to\\xE1n\",onChange:e=>setPaymentMethod(Number(e.target.value)),children:Object.entries(PaymentMethodMap).map(_ref2=>{let[value,label]=_ref2;return/*#__PURE__*/_jsx(MenuItem,{value:value,children:label},value);})})]}),/*#__PURE__*/_jsx(Box,{sx:{gridColumn:{xs:'1',sm:'1 / span 2'}},children:/*#__PURE__*/_jsx(TextField,{label:\"Ghi ch\\xFA\",fullWidth:true,multiline:true,rows:3,value:note,onChange:e=>setNote(e.target.value),placeholder:\"Nh\\u1EADp ghi ch\\xFA v\\u1EC1 thanh to\\xE1n (n\\u1EBFu c\\xF3)\",onKeyDown:e=>{// Prevent Enter from submitting in multiline text field\nif(e.key==='Enter'&&!e.shiftKey){e.stopPropagation();}}})})]})]})]}),/*#__PURE__*/_jsxs(DialogActions,{sx:{px:3,py:2,justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(Button,{onClick:onClose,color:\"inherit\",variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(CloseIcon,{}),children:\"H\\u1EE7y\"}),/*#__PURE__*/_jsx(Button,{onClick:handleSubmit,variant:\"contained\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(PaymentIcon,{}),size:\"large\",disabled:loading,children:loading?'Đang xử lý...':'Xác nhận thanh toán'})]})]});};export default PaymentForm;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "<PERSON><PERSON>", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Typography", "Paper", "Divider", "<PERSON><PERSON>", "IconButton", "CloseIcon", "PaymentIcon", "ReceiptIcon", "InfoOutlinedIcon", "PaymentMethodMap", "formatCurrency", "formatDateLocalized", "jsx", "_jsx", "jsxs", "_jsxs", "PaymentForm", "_ref", "open", "contract", "onClose", "onSubmit", "remainingAmount", "loading", "paymentAmount", "setPaymentAmount", "paymentMethod", "setPaymentMethod", "note", "setNote", "error", "setError", "handleSubmit", "e", "preventDefault", "concat", "payment", "paymentDate", "Date", "toISOString", "undefined", "customerContractId", "id", "customerId", "formatDate", "dateString", "max<PERSON><PERSON><PERSON>", "fullWidth", "sx", "borderRadius", "boxShadow", "children", "bgcolor", "color", "display", "justifyContent", "alignItems", "px", "py", "gap", "variant", "edge", "onClick", "p", "mb", "fontWeight", "gutterBottom", "fontSize", "gridTemplateColumns", "xs", "sm", "customerName", "startingDate", "endingDate", "my", "borderColor", "totalAmount", "totalPaid", "severity", "component", "label", "type", "value", "onChange", "Number", "target", "onKeyDown", "key", "paddingRight", "position", "content", "right", "top", "transform", "pointerEvents", "required", "Object", "entries", "map", "_ref2", "gridColumn", "multiline", "rows", "placeholder", "shift<PERSON>ey", "stopPropagation", "startIcon", "size", "disabled"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/payment/PaymentForm.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Button,\n  Dialog,\n  <PERSON>alogActions,\n  DialogContent,\n  DialogTitle,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Typography,\n  Paper,\n  Divider,\n  Alert,\n  IconButton,\n} from '@mui/material';\n\nimport CloseIcon from '@mui/icons-material/Close';\nimport PaymentIcon from '@mui/icons-material/Payment';\nimport ReceiptIcon from '@mui/icons-material/Receipt';\nimport InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';\nimport { CustomerContract, CustomerPayment, PaymentMethodMap } from '../../models';\nimport { formatCurrency } from '../../utils/formatters';\nimport { formatDateLocalized } from '../../utils/dateUtils';\n\ninterface PaymentFormProps {\n  open: boolean;\n  contract: CustomerContract | null;\n  onClose: () => void;\n  onSubmit: (payment: CustomerPayment) => void;\n  remainingAmount: number;\n  loading?: boolean;\n}\n\nconst PaymentForm: React.FC<PaymentFormProps> = ({\n  open,\n  contract,\n  onClose,\n  onSubmit,\n  remainingAmount,\n  loading = false,\n}) => {\n\n\n  const [paymentAmount, setPaymentAmount] = useState<number>(0);\n  const [paymentMethod, setPaymentMethod] = useState<number>(0); // Default: Tiền mặt\n  const [note, setNote] = useState<string>('');\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (open && contract) {\n      // Reset form when dialog opens\n      setPaymentAmount(remainingAmount);\n      setPaymentMethod(0);\n      setNote('');\n      setError(null);\n    }\n  }, [open, contract, remainingAmount]);\n\n  const handleSubmit = (e?: React.FormEvent) => {\n    // Prevent default form submission behavior\n    if (e) {\n      e.preventDefault();\n    }\n\n    if (!contract || loading) return;\n\n    // Validate payment amount\n    if (!paymentAmount || paymentAmount <= 0) {\n      setError('Số tiền thanh toán phải lớn hơn 0');\n      return;\n    }\n\n    if (paymentAmount > remainingAmount) {\n      setError(`Số tiền thanh toán không được vượt quá số tiền còn lại (${formatCurrency(remainingAmount)})`);\n      return;\n    }\n\n    // Create payment object\n    const payment: CustomerPayment = {\n      paymentDate: new Date().toISOString(),\n      paymentMethod,\n      paymentAmount,\n      note: note || undefined,\n      customerContractId: contract.id!,\n      customerId: contract.customerId,\n    };\n\n    onSubmit(payment);\n  };\n\n  const formatDate = (dateString?: string) => {\n    if (!dateString) return '-';\n    return formatDateLocalized(dateString);\n  };\n\n\n\n  if (!contract) return null;\n\n  return (\n    <Dialog\n      open={open}\n      onClose={onClose}\n      maxWidth=\"md\"\n      fullWidth\n      sx={{\n        '& .MuiDialog-paper': {\n          borderRadius: 2,\n          boxShadow: 24,\n        }\n      }}\n    >\n      <DialogTitle sx={{\n        bgcolor: 'primary.main',\n        color: 'primary.contrastText',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        px: 3,\n        py: 2\n      }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <PaymentIcon />\n          <Typography variant=\"h6\">Thanh toán hợp đồng</Typography>\n        </Box>\n        <IconButton\n          edge=\"end\"\n          color=\"inherit\"\n          onClick={onClose}\n          aria-label=\"close\"\n        >\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n\n      <DialogContent sx={{ p: 3 }}>\n        <Paper variant=\"outlined\" sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"subtitle1\" fontWeight=\"bold\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoOutlinedIcon fontSize=\"small\" color=\"primary\" />\n            Thông tin hợp đồng\n          </Typography>\n\n          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2 }}>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Mã hợp đồng\n              </Typography>\n              <Typography variant=\"body1\" fontWeight=\"medium\">\n                #{contract.id}\n              </Typography>\n            </Box>\n\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Khách hàng\n              </Typography>\n              <Typography variant=\"body1\" fontWeight=\"medium\">\n                {contract.customerName}\n              </Typography>\n            </Box>\n\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Ngày bắt đầu\n              </Typography>\n              <Typography variant=\"body1\">\n                {formatDate(contract.startingDate)}\n              </Typography>\n            </Box>\n\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Ngày kết thúc\n              </Typography>\n              <Typography variant=\"body1\">\n                {formatDate(contract.endingDate)}\n              </Typography>\n            </Box>\n\n\n          </Box>\n\n          <Divider sx={{ my: 2 }} />\n\n          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr 1fr' }, gap: 2 }}>\n            <Paper\n              variant=\"outlined\"\n              sx={{\n                p: 1.5,\n                bgcolor: 'background.default',\n                borderColor: 'divider'\n              }}\n            >\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Tổng giá trị hợp đồng\n              </Typography>\n              <Typography variant=\"h6\">\n                {formatCurrency(contract.totalAmount)}\n              </Typography>\n            </Paper>\n\n            <Paper\n              variant=\"outlined\"\n              sx={{\n                p: 1.5,\n                bgcolor: 'background.default',\n                borderColor: 'divider'\n              }}\n            >\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Đã thanh toán\n              </Typography>\n              <Typography variant=\"h6\">\n                {formatCurrency(contract.totalPaid || 0)}\n              </Typography>\n            </Paper>\n\n            <Paper\n              sx={{\n                p: 1.5,\n                bgcolor: 'primary.light',\n                color: 'primary.contrastText',\n                borderRadius: 1\n              }}\n            >\n              <Typography variant=\"body2\" color=\"inherit\">\n                Còn lại cần thanh toán\n              </Typography>\n              <Typography variant=\"h6\" fontWeight=\"bold\">\n                {formatCurrency(remainingAmount)}\n              </Typography>\n            </Paper>\n          </Box>\n        </Paper>\n\n        <Paper variant=\"outlined\" sx={{ p: 2, mb: 2 }}>\n          <Typography variant=\"subtitle1\" fontWeight=\"bold\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <ReceiptIcon fontSize=\"small\" color=\"primary\" />\n            Thông tin thanh toán\n          </Typography>\n\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              {error}\n            </Alert>\n          )}\n\n          <Box\n            component=\"form\"\n            onSubmit={handleSubmit}\n            sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 3 }}\n          >\n            <TextField\n              label=\"Số tiền thanh toán\"\n              type=\"number\"\n              fullWidth\n              value={paymentAmount}\n              onChange={(e) => setPaymentAmount(Number(e.target.value))}\n              onKeyDown={(e) => {\n                if (e.key === 'Enter') {\n                  e.preventDefault();\n                  handleSubmit();\n                }\n              }}\n              sx={{\n                '& .MuiInputBase-input': { paddingRight: '40px' },\n                '& .MuiInputBase-root': {\n                  position: 'relative',\n                  '&::after': {\n                    content: '\"VND\"',\n                    position: 'absolute',\n                    right: '14px',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: 'rgba(0, 0, 0, 0.54)',\n                    pointerEvents: 'none'\n                  }\n                }\n              }}\n              error={!!error}\n              required\n            />\n\n            <FormControl fullWidth required>\n              <InputLabel>Phương thức thanh toán</InputLabel>\n              <Select\n                value={paymentMethod}\n                label=\"Phương thức thanh toán\"\n                onChange={(e) => setPaymentMethod(Number(e.target.value))}\n              >\n                {Object.entries(PaymentMethodMap).map(([value, label]) => (\n                  <MenuItem key={value} value={value}>\n                    {label}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n\n            <Box sx={{ gridColumn: { xs: '1', sm: '1 / span 2' } }}>\n              <TextField\n                label=\"Ghi chú\"\n                fullWidth\n                multiline\n                rows={3}\n                value={note}\n                onChange={(e) => setNote(e.target.value)}\n                placeholder=\"Nhập ghi chú về thanh toán (nếu có)\"\n                onKeyDown={(e) => {\n                  // Prevent Enter from submitting in multiline text field\n                  if (e.key === 'Enter' && !e.shiftKey) {\n                    e.stopPropagation();\n                  }\n                }}\n              />\n            </Box>\n          </Box>\n        </Paper>\n      </DialogContent>\n\n      <DialogActions sx={{ px: 3, py: 2, justifyContent: 'space-between' }}>\n        <Button\n          onClick={onClose}\n          color=\"inherit\"\n          variant=\"outlined\"\n          startIcon={<CloseIcon />}\n        >\n          Hủy\n        </Button>\n        <Button\n          onClick={handleSubmit}\n          variant=\"contained\"\n          color=\"primary\"\n          startIcon={<PaymentIcon />}\n          size=\"large\"\n          disabled={loading}\n        >\n          {loading ? 'Đang xử lý...' : 'Xác nhận thanh toán'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default PaymentForm;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,MAAM,CACNC,MAAM,CACNC,aAAa,CACbC,aAAa,CACbC,WAAW,CACXC,SAAS,CACTC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,CACRC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,KAAK,CACLC,UAAU,KACL,eAAe,CAEtB,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,gBAAgB,KAAM,kCAAkC,CAC/D,OAA4CC,gBAAgB,KAAQ,cAAc,CAClF,OAASC,cAAc,KAAQ,wBAAwB,CACvD,OAASC,mBAAmB,KAAQ,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAW5D,KAAM,CAAAC,WAAuC,CAAGC,IAAA,EAO1C,IAP2C,CAC/CC,IAAI,CACJC,QAAQ,CACRC,OAAO,CACPC,QAAQ,CACRC,eAAe,CACfC,OAAO,CAAG,KACZ,CAAC,CAAAN,IAAA,CAGC,KAAM,CAACO,aAAa,CAAEC,gBAAgB,CAAC,CAAGtC,QAAQ,CAAS,CAAC,CAAC,CAC7D,KAAM,CAACuC,aAAa,CAAEC,gBAAgB,CAAC,CAAGxC,QAAQ,CAAS,CAAC,CAAC,CAAE;AAC/D,KAAM,CAACyC,IAAI,CAAEC,OAAO,CAAC,CAAG1C,QAAQ,CAAS,EAAE,CAAC,CAC5C,KAAM,CAAC2C,KAAK,CAAEC,QAAQ,CAAC,CAAG5C,QAAQ,CAAgB,IAAI,CAAC,CAEvDC,SAAS,CAAC,IAAM,CACd,GAAI8B,IAAI,EAAIC,QAAQ,CAAE,CACpB;AACAM,gBAAgB,CAACH,eAAe,CAAC,CACjCK,gBAAgB,CAAC,CAAC,CAAC,CACnBE,OAAO,CAAC,EAAE,CAAC,CACXE,QAAQ,CAAC,IAAI,CAAC,CAChB,CACF,CAAC,CAAE,CAACb,IAAI,CAAEC,QAAQ,CAAEG,eAAe,CAAC,CAAC,CAErC,KAAM,CAAAU,YAAY,CAAIC,CAAmB,EAAK,CAC5C;AACA,GAAIA,CAAC,CAAE,CACLA,CAAC,CAACC,cAAc,CAAC,CAAC,CACpB,CAEA,GAAI,CAACf,QAAQ,EAAII,OAAO,CAAE,OAE1B;AACA,GAAI,CAACC,aAAa,EAAIA,aAAa,EAAI,CAAC,CAAE,CACxCO,QAAQ,CAAC,mCAAmC,CAAC,CAC7C,OACF,CAEA,GAAIP,aAAa,CAAGF,eAAe,CAAE,CACnCS,QAAQ,0HAAAI,MAAA,CAA4DzB,cAAc,CAACY,eAAe,CAAC,KAAG,CAAC,CACvG,OACF,CAEA;AACA,KAAM,CAAAc,OAAwB,CAAG,CAC/BC,WAAW,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACrCb,aAAa,CACbF,aAAa,CACbI,IAAI,CAAEA,IAAI,EAAIY,SAAS,CACvBC,kBAAkB,CAAEtB,QAAQ,CAACuB,EAAG,CAChCC,UAAU,CAAExB,QAAQ,CAACwB,UACvB,CAAC,CAEDtB,QAAQ,CAACe,OAAO,CAAC,CACnB,CAAC,CAED,KAAM,CAAAQ,UAAU,CAAIC,UAAmB,EAAK,CAC1C,GAAI,CAACA,UAAU,CAAE,MAAO,GAAG,CAC3B,MAAO,CAAAlC,mBAAmB,CAACkC,UAAU,CAAC,CACxC,CAAC,CAID,GAAI,CAAC1B,QAAQ,CAAE,MAAO,KAAI,CAE1B,mBACEJ,KAAA,CAACxB,MAAM,EACL2B,IAAI,CAAEA,IAAK,CACXE,OAAO,CAAEA,OAAQ,CACjB0B,QAAQ,CAAC,IAAI,CACbC,SAAS,MACTC,EAAE,CAAE,CACF,oBAAoB,CAAE,CACpBC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE,EACb,CACF,CAAE,CAAAC,QAAA,eAEFpC,KAAA,CAACrB,WAAW,EAACsD,EAAE,CAAE,CACfI,OAAO,CAAE,cAAc,CACvBC,KAAK,CAAE,sBAAsB,CAC7BC,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAE,CAAAP,QAAA,eACApC,KAAA,CAAC1B,GAAG,EAAC2D,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEG,GAAG,CAAE,CAAE,CAAE,CAAAR,QAAA,eACzDtC,IAAA,CAACP,WAAW,GAAE,CAAC,cACfO,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,IAAI,CAAAT,QAAA,CAAC,uCAAmB,CAAY,CAAC,EACtD,CAAC,cACNtC,IAAA,CAACT,UAAU,EACTyD,IAAI,CAAC,KAAK,CACVR,KAAK,CAAC,SAAS,CACfS,OAAO,CAAE1C,OAAQ,CACjB,aAAW,OAAO,CAAA+B,QAAA,cAElBtC,IAAA,CAACR,SAAS,GAAE,CAAC,CACH,CAAC,EACF,CAAC,cAEdU,KAAA,CAACtB,aAAa,EAACuD,EAAE,CAAE,CAAEe,CAAC,CAAE,CAAE,CAAE,CAAAZ,QAAA,eAC1BpC,KAAA,CAACd,KAAK,EAAC2D,OAAO,CAAC,UAAU,CAACZ,EAAE,CAAE,CAAEe,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eAC5CpC,KAAA,CAACf,UAAU,EAAC4D,OAAO,CAAC,WAAW,CAACK,UAAU,CAAC,MAAM,CAACC,YAAY,MAAClB,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEG,GAAG,CAAE,CAAE,CAAE,CAAAR,QAAA,eACnHtC,IAAA,CAACL,gBAAgB,EAAC2D,QAAQ,CAAC,OAAO,CAACd,KAAK,CAAC,SAAS,CAAE,CAAC,uCAEvD,EAAY,CAAC,cAEbtC,KAAA,CAAC1B,GAAG,EAAC2D,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEc,mBAAmB,CAAE,CAAEC,EAAE,CAAE,KAAK,CAAEC,EAAE,CAAE,SAAU,CAAC,CAAEX,GAAG,CAAE,CAAE,CAAE,CAAAR,QAAA,eACtFpC,KAAA,CAAC1B,GAAG,EAAA8D,QAAA,eACFtC,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,OAAO,CAACP,KAAK,CAAC,gBAAgB,CAAAF,QAAA,CAAC,+BAEnD,CAAY,CAAC,cACbpC,KAAA,CAACf,UAAU,EAAC4D,OAAO,CAAC,OAAO,CAACK,UAAU,CAAC,QAAQ,CAAAd,QAAA,EAAC,GAC7C,CAAChC,QAAQ,CAACuB,EAAE,EACH,CAAC,EACV,CAAC,cAEN3B,KAAA,CAAC1B,GAAG,EAAA8D,QAAA,eACFtC,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,OAAO,CAACP,KAAK,CAAC,gBAAgB,CAAAF,QAAA,CAAC,kBAEnD,CAAY,CAAC,cACbtC,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,OAAO,CAACK,UAAU,CAAC,QAAQ,CAAAd,QAAA,CAC5ChC,QAAQ,CAACoD,YAAY,CACZ,CAAC,EACV,CAAC,cAENxD,KAAA,CAAC1B,GAAG,EAAA8D,QAAA,eACFtC,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,OAAO,CAACP,KAAK,CAAC,gBAAgB,CAAAF,QAAA,CAAC,gCAEnD,CAAY,CAAC,cACbtC,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,OAAO,CAAAT,QAAA,CACxBP,UAAU,CAACzB,QAAQ,CAACqD,YAAY,CAAC,CACxB,CAAC,EACV,CAAC,cAENzD,KAAA,CAAC1B,GAAG,EAAA8D,QAAA,eACFtC,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,OAAO,CAACP,KAAK,CAAC,gBAAgB,CAAAF,QAAA,CAAC,0BAEnD,CAAY,CAAC,cACbtC,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,OAAO,CAAAT,QAAA,CACxBP,UAAU,CAACzB,QAAQ,CAACsD,UAAU,CAAC,CACtB,CAAC,EACV,CAAC,EAGH,CAAC,cAEN5D,IAAA,CAACX,OAAO,EAAC8C,EAAE,CAAE,CAAE0B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAE1B3D,KAAA,CAAC1B,GAAG,EAAC2D,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEc,mBAAmB,CAAE,CAAEC,EAAE,CAAE,KAAK,CAAEC,EAAE,CAAE,aAAc,CAAC,CAAEX,GAAG,CAAE,CAAE,CAAE,CAAAR,QAAA,eAC1FpC,KAAA,CAACd,KAAK,EACJ2D,OAAO,CAAC,UAAU,CAClBZ,EAAE,CAAE,CACFe,CAAC,CAAE,GAAG,CACNX,OAAO,CAAE,oBAAoB,CAC7BuB,WAAW,CAAE,SACf,CAAE,CAAAxB,QAAA,eAEFtC,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,OAAO,CAACP,KAAK,CAAC,gBAAgB,CAAAF,QAAA,CAAC,mDAEnD,CAAY,CAAC,cACbtC,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,IAAI,CAAAT,QAAA,CACrBzC,cAAc,CAACS,QAAQ,CAACyD,WAAW,CAAC,CAC3B,CAAC,EACR,CAAC,cAER7D,KAAA,CAACd,KAAK,EACJ2D,OAAO,CAAC,UAAU,CAClBZ,EAAE,CAAE,CACFe,CAAC,CAAE,GAAG,CACNX,OAAO,CAAE,oBAAoB,CAC7BuB,WAAW,CAAE,SACf,CAAE,CAAAxB,QAAA,eAEFtC,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,OAAO,CAACP,KAAK,CAAC,gBAAgB,CAAAF,QAAA,CAAC,0BAEnD,CAAY,CAAC,cACbtC,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,IAAI,CAAAT,QAAA,CACrBzC,cAAc,CAACS,QAAQ,CAAC0D,SAAS,EAAI,CAAC,CAAC,CAC9B,CAAC,EACR,CAAC,cAER9D,KAAA,CAACd,KAAK,EACJ+C,EAAE,CAAE,CACFe,CAAC,CAAE,GAAG,CACNX,OAAO,CAAE,eAAe,CACxBC,KAAK,CAAE,sBAAsB,CAC7BJ,YAAY,CAAE,CAChB,CAAE,CAAAE,QAAA,eAEFtC,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,OAAO,CAACP,KAAK,CAAC,SAAS,CAAAF,QAAA,CAAC,wCAE5C,CAAY,CAAC,cACbtC,IAAA,CAACb,UAAU,EAAC4D,OAAO,CAAC,IAAI,CAACK,UAAU,CAAC,MAAM,CAAAd,QAAA,CACvCzC,cAAc,CAACY,eAAe,CAAC,CACtB,CAAC,EACR,CAAC,EACL,CAAC,EACD,CAAC,cAERP,KAAA,CAACd,KAAK,EAAC2D,OAAO,CAAC,UAAU,CAACZ,EAAE,CAAE,CAAEe,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eAC5CpC,KAAA,CAACf,UAAU,EAAC4D,OAAO,CAAC,WAAW,CAACK,UAAU,CAAC,MAAM,CAACC,YAAY,MAAClB,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEG,GAAG,CAAE,CAAE,CAAE,CAAAR,QAAA,eACnHtC,IAAA,CAACN,WAAW,EAAC4D,QAAQ,CAAC,OAAO,CAACd,KAAK,CAAC,SAAS,CAAE,CAAC,6BAElD,EAAY,CAAC,CAEZvB,KAAK,eACJjB,IAAA,CAACV,KAAK,EAAC2E,QAAQ,CAAC,OAAO,CAAC9B,EAAE,CAAE,CAAEgB,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,CACnCrB,KAAK,CACD,CACR,cAEDf,KAAA,CAAC1B,GAAG,EACF0F,SAAS,CAAC,MAAM,CAChB1D,QAAQ,CAAEW,YAAa,CACvBgB,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEc,mBAAmB,CAAE,CAAEC,EAAE,CAAE,KAAK,CAAEC,EAAE,CAAE,SAAU,CAAC,CAAEX,GAAG,CAAE,CAAE,CAAE,CAAAR,QAAA,eAEnFtC,IAAA,CAAClB,SAAS,EACRqF,KAAK,CAAC,iCAAoB,CAC1BC,IAAI,CAAC,QAAQ,CACblC,SAAS,MACTmC,KAAK,CAAE1D,aAAc,CACrB2D,QAAQ,CAAGlD,CAAC,EAAKR,gBAAgB,CAAC2D,MAAM,CAACnD,CAAC,CAACoD,MAAM,CAACH,KAAK,CAAC,CAAE,CAC1DI,SAAS,CAAGrD,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACsD,GAAG,GAAK,OAAO,CAAE,CACrBtD,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBF,YAAY,CAAC,CAAC,CAChB,CACF,CAAE,CACFgB,EAAE,CAAE,CACF,uBAAuB,CAAE,CAAEwC,YAAY,CAAE,MAAO,CAAC,CACjD,sBAAsB,CAAE,CACtBC,QAAQ,CAAE,UAAU,CACpB,UAAU,CAAE,CACVC,OAAO,CAAE,OAAO,CAChBD,QAAQ,CAAE,UAAU,CACpBE,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,KAAK,CACVC,SAAS,CAAE,kBAAkB,CAC7BxC,KAAK,CAAE,qBAAqB,CAC5ByC,aAAa,CAAE,MACjB,CACF,CACF,CAAE,CACFhE,KAAK,CAAE,CAAC,CAACA,KAAM,CACfiE,QAAQ,MACT,CAAC,cAEFhF,KAAA,CAACnB,WAAW,EAACmD,SAAS,MAACgD,QAAQ,MAAA5C,QAAA,eAC7BtC,IAAA,CAAChB,UAAU,EAAAsD,QAAA,CAAC,0CAAsB,CAAY,CAAC,cAC/CtC,IAAA,CAACf,MAAM,EACLoF,KAAK,CAAExD,aAAc,CACrBsD,KAAK,CAAC,0CAAwB,CAC9BG,QAAQ,CAAGlD,CAAC,EAAKN,gBAAgB,CAACyD,MAAM,CAACnD,CAAC,CAACoD,MAAM,CAACH,KAAK,CAAC,CAAE,CAAA/B,QAAA,CAEzD6C,MAAM,CAACC,OAAO,CAACxF,gBAAgB,CAAC,CAACyF,GAAG,CAACC,KAAA,MAAC,CAACjB,KAAK,CAAEF,KAAK,CAAC,CAAAmB,KAAA,oBACnDtF,IAAA,CAACd,QAAQ,EAAamF,KAAK,CAAEA,KAAM,CAAA/B,QAAA,CAChC6B,KAAK,EADOE,KAEL,CAAC,EACZ,CAAC,CACI,CAAC,EACE,CAAC,cAEdrE,IAAA,CAACxB,GAAG,EAAC2D,EAAE,CAAE,CAAEoD,UAAU,CAAE,CAAE/B,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,YAAa,CAAE,CAAE,CAAAnB,QAAA,cACrDtC,IAAA,CAAClB,SAAS,EACRqF,KAAK,CAAC,YAAS,CACfjC,SAAS,MACTsD,SAAS,MACTC,IAAI,CAAE,CAAE,CACRpB,KAAK,CAAEtD,IAAK,CACZuD,QAAQ,CAAGlD,CAAC,EAAKJ,OAAO,CAACI,CAAC,CAACoD,MAAM,CAACH,KAAK,CAAE,CACzCqB,WAAW,CAAC,6DAAqC,CACjDjB,SAAS,CAAGrD,CAAC,EAAK,CAChB;AACA,GAAIA,CAAC,CAACsD,GAAG,GAAK,OAAO,EAAI,CAACtD,CAAC,CAACuE,QAAQ,CAAE,CACpCvE,CAAC,CAACwE,eAAe,CAAC,CAAC,CACrB,CACF,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,EACD,CAAC,EACK,CAAC,cAEhB1F,KAAA,CAACvB,aAAa,EAACwD,EAAE,CAAE,CAAES,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEH,cAAc,CAAE,eAAgB,CAAE,CAAAJ,QAAA,eACnEtC,IAAA,CAACvB,MAAM,EACLwE,OAAO,CAAE1C,OAAQ,CACjBiC,KAAK,CAAC,SAAS,CACfO,OAAO,CAAC,UAAU,CAClB8C,SAAS,cAAE7F,IAAA,CAACR,SAAS,GAAE,CAAE,CAAA8C,QAAA,CAC1B,UAED,CAAQ,CAAC,cACTtC,IAAA,CAACvB,MAAM,EACLwE,OAAO,CAAE9B,YAAa,CACtB4B,OAAO,CAAC,WAAW,CACnBP,KAAK,CAAC,SAAS,CACfqD,SAAS,cAAE7F,IAAA,CAACP,WAAW,GAAE,CAAE,CAC3BqG,IAAI,CAAC,OAAO,CACZC,QAAQ,CAAErF,OAAQ,CAAA4B,QAAA,CAEjB5B,OAAO,CAAG,eAAe,CAAG,qBAAqB,CAC5C,CAAC,EACI,CAAC,EACV,CAAC,CAEb,CAAC,CAED,cAAe,CAAAP,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}