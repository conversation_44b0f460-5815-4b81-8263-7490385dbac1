{"ast": null, "code": "import _objectSpread from\"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import axios from'axios';// Kiểm tra môi trường và cấu hình URL phù hợp\nconst getBaseUrl=()=>{// Nếu đang chạy trong môi trường phát triển (localhost)\nif(window.location.hostname==='localhost'||window.location.hostname==='127.0.0.1'){// Kiểm tra xem API Gateway có hoạt động không\nconst apiGatewayUrl=process.env.REACT_APP_API_URL||'http://localhost:8080';console.log('Using API Gateway URL:',apiGatewayUrl);return apiGatewayUrl;}// Nếu đang chạy trong môi trường production, sử dụng URL tương đối\nreturn'';};// Kiểm tra xem API Gateway có hoạt động không\nconst checkApiGateway=async()=>{try{const response=await axios.get(\"\".concat(getBaseUrl(),\"/actuator/health\"),{timeout:3000,headers:{'Accept':'application/json'}});return response.status===200;}catch(error){console.error('API Gateway health check failed:',error);return false;}};// Create a base API client instance\nconst apiClient=axios.create({baseURL:getBaseUrl(),// API gateway URL\nheaders:{'Content-Type':'application/json','Accept':'application/json','X-Requested-With':'XMLHttpRequest'// Giúp một số máy chủ nhận biết đây là AJAX request\n},timeout:30000,// Tăng timeout lên 30 seconds\nwithCredentials:false// Không gửi cookie trong cross-origin requests\n});// Request interceptor for API calls\napiClient.interceptors.request.use(config=>{var _config$method;// Log the request for debugging\nconsole.log(\"API Request: \".concat((_config$method=config.method)===null||_config$method===void 0?void 0:_config$method.toUpperCase(),\" \").concat(config.url));return config;},error=>{console.error('Request error:',error);return Promise.reject(error);});// Response interceptor for API calls\napiClient.interceptors.response.use(response=>{// Log successful responses for debugging\nconsole.log(\"API Response: \".concat(response.status,\" \").concat(response.config.url));// Kiểm tra nếu response là JSON hợp lệ\ntry{if(typeof response.data==='string'&&response.data.trim()!==''){console.warn('Response is string, attempting to parse as JSON:',response.data);response.data=JSON.parse(response.data);}}catch(e){console.error('Failed to parse response data as JSON:',e);}return response;},error=>{// Handle errors globally\nif(axios.isAxiosError(error)){var _error$response,_error$config,_error$config2,_error$response2,_error$response3,_error$response4,_error$response5,_error$response6,_error$response7;const errorInfo={message:error.message,status:(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status,url:(_error$config=error.config)===null||_error$config===void 0?void 0:_error$config.url,method:(_error$config2=error.config)===null||_error$config2===void 0?void 0:_error$config2.method,data:(_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.data,code:error.code};console.error('API Error:',errorInfo);// Xử lý thông báo lỗi từ backend\nif((_error$response3=error.response)!==null&&_error$response3!==void 0&&_error$response3.data){// Nếu backend trả về thông báo lỗi cụ thể\nif(typeof error.response.data==='string'){error.message=error.response.data;}else if(error.response.data.message){error.message=error.response.data.message;}else if(error.response.data.error){error.message=error.response.data.error;}}// Chi tiết hơn về các loại lỗi\nif(error.code==='ECONNABORTED'){console.error('Request timeout. The server took too long to respond.');error.message='Yêu cầu đã hết thời gian chờ. Máy chủ mất quá nhiều thời gian để phản hồi.';}else if(error.message.includes('Network Error')||!error.response){console.error('Network error. Please check your connection or the server might be down.');// Kiểm tra lỗi CORS\nif(error.message.includes('CORS')){console.error('CORS error detected. This might be a cross-origin issue.');error.message='Lỗi CORS: Không thể kết nối đến máy chủ do chính sách bảo mật trình duyệt.';}else{error.message='Lỗi kết nối mạng. Vui lòng kiểm tra kết nối của bạn hoặc máy chủ có thể đang gặp sự cố.';}}else if(((_error$response4=error.response)===null||_error$response4===void 0?void 0:_error$response4.status)===404){var _error$response$data;if(!((_error$response$data=error.response.data)!==null&&_error$response$data!==void 0&&_error$response$data.message)){error.message='Không tìm thấy tài nguyên yêu cầu.';}}else if(((_error$response5=error.response)===null||_error$response5===void 0?void 0:_error$response5.status)===500){var _error$response$data2;if(!((_error$response$data2=error.response.data)!==null&&_error$response$data2!==void 0&&_error$response$data2.message)){error.message='Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';}}else if(((_error$response6=error.response)===null||_error$response6===void 0?void 0:_error$response6.status)===403){var _error$response$data3;if(!((_error$response$data3=error.response.data)!==null&&_error$response$data3!==void 0&&_error$response$data3.message)){error.message='Bạn không có quyền truy cập tài nguyên này.';}}else if(((_error$response7=error.response)===null||_error$response7===void 0?void 0:_error$response7.status)===400){var _error$response$data4;// Lỗi validation hoặc business logic từ backend\nif(!((_error$response$data4=error.response.data)!==null&&_error$response$data4!==void 0&&_error$response$data4.message)){error.message='Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.';}}}else{console.error('Unexpected error:',error);}return Promise.reject(error);});// Generic GET request\nexport const get=async(url,config)=>{try{// Thêm log để debug\nconsole.log(\"Making GET request to: \".concat(getBaseUrl()).concat(url),config);// Thử gọi API qua API Gateway\ntry{const response=await apiClient.get(url,config);console.log(\"GET request to \".concat(url,\" succeeded with status:\"),response.status);return response.data;}catch(gatewayError){console.error(\"GET request failed through API Gateway for \".concat(url,\":\"),gatewayError);// Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\nlet directUrl=null;if(url.startsWith('/api/customer-statistics')){directUrl=\"http://localhost:8085\".concat(url);console.log(\"Trying direct connection to customer-statistics-service for \".concat(url));}else if(url.startsWith('/api/customer-contract')||url.startsWith('/api/job-detail')||url.startsWith('/api/work-shift')){directUrl=\"http://localhost:8083\".concat(url);console.log(\"Trying direct connection to customer-contract-service for \".concat(url));}else if(url.startsWith('/api/customer-payment')){directUrl=\"http://localhost:8084\".concat(url);console.log(\"Trying direct connection to customer-payment-service for \".concat(url));}else if(url.startsWith('/api/customer')){directUrl=\"http://localhost:8081\".concat(url);console.log(\"Trying direct connection to customer-service for \".concat(url));}else if(url.startsWith('/api/job')||url.startsWith('/api/job-category')){directUrl=\"http://localhost:8082\".concat(url);console.log(\"Trying direct connection to job-service for \".concat(url));}if(directUrl){console.log(\"Direct URL: \".concat(directUrl));// Gọi trực tiếp đến service\nconst directResponse=await axios.get(directUrl,_objectSpread(_objectSpread({},config),{},{headers:_objectSpread(_objectSpread({},config===null||config===void 0?void 0:config.headers),{},{'Accept':'application/json','Content-Type':'application/json'})}));console.log(\"Direct GET request to \".concat(directUrl,\" succeeded with status:\"),directResponse.status);return directResponse.data;}// Nếu không có service phù hợp, ném lỗi\nthrow gatewayError;}}catch(error){console.error(\"GET request failed for \".concat(url,\":\"),error);throw error;}};// Generic POST request\nexport const post=async(url,data,config)=>{try{const response=await apiClient.post(url,data,config);return response.data;}catch(gatewayError){console.error(\"POST request failed through API Gateway for \".concat(url,\":\"),gatewayError);// Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\nlet directUrl=null;if(url.startsWith('/api/customer-contract')||url.startsWith('/api/job-detail')||url.startsWith('/api/work-shift')){directUrl=\"http://localhost:8083\".concat(url);}else if(url.startsWith('/api/customer-payment')){directUrl=\"http://localhost:8084\".concat(url);}else if(url.startsWith('/api/customer')){directUrl=\"http://localhost:8081\".concat(url);}else if(url.startsWith('/api/job')||url.startsWith('/api/job-category')){directUrl=\"http://localhost:8082\".concat(url);}if(directUrl){try{const directResponse=await axios.post(directUrl,data,_objectSpread(_objectSpread({},config),{},{headers:_objectSpread(_objectSpread({},config===null||config===void 0?void 0:config.headers),{},{'Accept':'application/json','Content-Type':'application/json'})}));return directResponse.data;}catch(directError){var _directError$response;// Xử lý lỗi từ direct connection\nif(axios.isAxiosError(directError)&&(_directError$response=directError.response)!==null&&_directError$response!==void 0&&_directError$response.data){if(typeof directError.response.data==='string'){directError.message=directError.response.data;}else if(directError.response.data.message){directError.message=directError.response.data.message;}}throw directError;}}throw gatewayError;}};// Generic PUT request\nexport const put=async(url,data,config)=>{try{const response=await apiClient.put(url,data,config);return response.data;}catch(gatewayError){console.error(\"PUT request failed through API Gateway for \".concat(url,\":\"),gatewayError);// Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\nlet directUrl=null;if(url.startsWith('/api/customer-contract')||url.startsWith('/api/job-detail')||url.startsWith('/api/work-shift')){directUrl=\"http://localhost:8083\".concat(url);}else if(url.startsWith('/api/customer-payment')){directUrl=\"http://localhost:8084\".concat(url);}else if(url.startsWith('/api/customer')){directUrl=\"http://localhost:8081\".concat(url);}else if(url.startsWith('/api/job')||url.startsWith('/api/job-category')){directUrl=\"http://localhost:8082\".concat(url);}if(directUrl){const directResponse=await axios.put(directUrl,data,_objectSpread(_objectSpread({},config),{},{headers:_objectSpread(_objectSpread({},config===null||config===void 0?void 0:config.headers),{},{'Accept':'application/json','Content-Type':'application/json'})}));return directResponse.data;}throw gatewayError;}};// Generic DELETE request\nexport const del=async(url,config)=>{try{const response=await apiClient.delete(url,config);return response.data;}catch(gatewayError){console.error(\"DELETE request failed through API Gateway for \".concat(url,\":\"),gatewayError);// Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\nlet directUrl=null;if(url.startsWith('/api/customer-contract')||url.startsWith('/api/job-detail')||url.startsWith('/api/work-shift')){directUrl=\"http://localhost:8083\".concat(url);}else if(url.startsWith('/api/customer-payment')){directUrl=\"http://localhost:8084\".concat(url);}else if(url.startsWith('/api/customer')){directUrl=\"http://localhost:8081\".concat(url);}else if(url.startsWith('/api/job')||url.startsWith('/api/job-category')){directUrl=\"http://localhost:8082\".concat(url);}if(directUrl){const directResponse=await axios.delete(directUrl,_objectSpread(_objectSpread({},config),{},{headers:_objectSpread(_objectSpread({},config===null||config===void 0?void 0:config.headers),{},{'Accept':'application/json','Content-Type':'application/json'})}));return directResponse.data;}throw gatewayError;}};export default apiClient;", "map": {"version": 3, "names": ["axios", "getBaseUrl", "window", "location", "hostname", "apiGatewayUrl", "process", "env", "REACT_APP_API_URL", "console", "log", "checkApiGateway", "response", "get", "concat", "timeout", "headers", "status", "error", "apiClient", "create", "baseURL", "withCredentials", "interceptors", "request", "use", "config", "_config$method", "method", "toUpperCase", "url", "Promise", "reject", "data", "trim", "warn", "JSON", "parse", "e", "isAxiosError", "_error$response", "_error$config", "_error$config2", "_error$response2", "_error$response3", "_error$response4", "_error$response5", "_error$response6", "_error$response7", "errorInfo", "message", "code", "includes", "_error$response$data", "_error$response$data2", "_error$response$data3", "_error$response$data4", "gatewayError", "directUrl", "startsWith", "directResponse", "_objectSpread", "post", "directError", "_directError$response", "put", "del", "delete"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/services/api/apiClient.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\n\n// Kiểm tra môi trường và cấu hình URL phù hợp\nconst getBaseUrl = () => {\n  // Nếu đang chạy trong môi trường phát triển (localhost)\n  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {\n    // Kiểm tra xem API Gateway có hoạt động không\n    const apiGatewayUrl = process.env.REACT_APP_API_URL || 'http://localhost:8080';\n    console.log('Using API Gateway URL:', apiGatewayUrl);\n    return apiGatewayUrl;\n  }\n\n  // Nếu đang chạy trong môi trường production, sử dụng URL tương đối\n  return '';\n};\n\n// Kiểm tra xem API Gateway có hoạt động không\nconst checkApiGateway = async (): Promise<boolean> => {\n  try {\n    const response = await axios.get(`${getBaseUrl()}/actuator/health`, {\n      timeout: 3000,\n      headers: {\n        'Accept': 'application/json'\n      }\n    });\n    return response.status === 200;\n  } catch (error) {\n    console.error('API Gateway health check failed:', error);\n    return false;\n  }\n};\n\n// Create a base API client instance\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: getBaseUrl(), // API gateway URL\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n    'X-Requested-With': 'XMLHttpRequest' // Giúp một số máy chủ nhận biết đây là AJAX request\n  },\n  timeout: 30000, // Tăng timeout lên 30 seconds\n  withCredentials: false // Không gửi cookie trong cross-origin requests\n});\n\n// Request interceptor for API calls\napiClient.interceptors.request.use(\n  (config) => {\n    // Log the request for debugging\n    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    return config;\n  },\n  (error) => {\n    console.error('Request error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for API calls\napiClient.interceptors.response.use(\n  (response) => {\n    // Log successful responses for debugging\n    console.log(`API Response: ${response.status} ${response.config.url}`);\n\n    // Kiểm tra nếu response là JSON hợp lệ\n    try {\n      if (typeof response.data === 'string' && response.data.trim() !== '') {\n        console.warn('Response is string, attempting to parse as JSON:', response.data);\n        response.data = JSON.parse(response.data);\n      }\n    } catch (e) {\n      console.error('Failed to parse response data as JSON:', e);\n    }\n\n    return response;\n  },\n  (error) => {\n    // Handle errors globally\n    if (axios.isAxiosError(error)) {\n      const errorInfo = {\n        message: error.message,\n        status: error.response?.status,\n        url: error.config?.url,\n        method: error.config?.method,\n        data: error.response?.data,\n        code: error.code\n      };\n\n      console.error('API Error:', errorInfo);\n\n      // Xử lý thông báo lỗi từ backend\n      if (error.response?.data) {\n        // Nếu backend trả về thông báo lỗi cụ thể\n        if (typeof error.response.data === 'string') {\n          error.message = error.response.data;\n        } else if (error.response.data.message) {\n          error.message = error.response.data.message;\n        } else if (error.response.data.error) {\n          error.message = error.response.data.error;\n        }\n      }\n\n      // Chi tiết hơn về các loại lỗi\n      if (error.code === 'ECONNABORTED') {\n        console.error('Request timeout. The server took too long to respond.');\n        error.message = 'Yêu cầu đã hết thời gian chờ. Máy chủ mất quá nhiều thời gian để phản hồi.';\n      } else if (error.message.includes('Network Error') || !error.response) {\n        console.error('Network error. Please check your connection or the server might be down.');\n\n        // Kiểm tra lỗi CORS\n        if (error.message.includes('CORS')) {\n          console.error('CORS error detected. This might be a cross-origin issue.');\n          error.message = 'Lỗi CORS: Không thể kết nối đến máy chủ do chính sách bảo mật trình duyệt.';\n        } else {\n          error.message = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối của bạn hoặc máy chủ có thể đang gặp sự cố.';\n        }\n      } else if (error.response?.status === 404) {\n        if (!error.response.data?.message) {\n          error.message = 'Không tìm thấy tài nguyên yêu cầu.';\n        }\n      } else if (error.response?.status === 500) {\n        if (!error.response.data?.message) {\n          error.message = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';\n        }\n      } else if (error.response?.status === 403) {\n        if (!error.response.data?.message) {\n          error.message = 'Bạn không có quyền truy cập tài nguyên này.';\n        }\n      } else if (error.response?.status === 400) {\n        // Lỗi validation hoặc business logic từ backend\n        if (!error.response.data?.message) {\n          error.message = 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.';\n        }\n      }\n    } else {\n      console.error('Unexpected error:', error);\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Generic GET request\nexport const get = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n  try {\n    // Thêm log để debug\n    console.log(`Making GET request to: ${getBaseUrl()}${url}`, config);\n\n    // Thử gọi API qua API Gateway\n    try {\n      const response: AxiosResponse<T> = await apiClient.get(url, config);\n      console.log(`GET request to ${url} succeeded with status:`, response.status);\n      return response.data;\n    } catch (gatewayError) {\n      console.error(`GET request failed through API Gateway for ${url}:`, gatewayError);\n\n      // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\n      let directUrl: string | null = null;\n\n      if (url.startsWith('/api/customer-statistics')) {\n        directUrl = `http://localhost:8085${url}`;\n        console.log(`Trying direct connection to customer-statistics-service for ${url}`);\n      } else if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {\n        directUrl = `http://localhost:8083${url}`;\n        console.log(`Trying direct connection to customer-contract-service for ${url}`);\n      } else if (url.startsWith('/api/customer-payment')) {\n        directUrl = `http://localhost:8084${url}`;\n        console.log(`Trying direct connection to customer-payment-service for ${url}`);\n      } else if (url.startsWith('/api/customer')) {\n        directUrl = `http://localhost:8081${url}`;\n        console.log(`Trying direct connection to customer-service for ${url}`);\n      } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {\n        directUrl = `http://localhost:8082${url}`;\n        console.log(`Trying direct connection to job-service for ${url}`);\n      }\n\n      if (directUrl) {\n        console.log(`Direct URL: ${directUrl}`);\n\n        // Gọi trực tiếp đến service\n        const directResponse = await axios.get(directUrl, {\n          ...config,\n          headers: {\n            ...config?.headers,\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n\n        console.log(`Direct GET request to ${directUrl} succeeded with status:`, directResponse.status);\n        return directResponse.data;\n      }\n\n      // Nếu không có service phù hợp, ném lỗi\n      throw gatewayError;\n    }\n  } catch (error) {\n    console.error(`GET request failed for ${url}:`, error);\n    throw error;\n  }\n};\n\n// Generic POST request\nexport const post = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n  try {\n    const response: AxiosResponse<T> = await apiClient.post(url, data, config);\n    return response.data;\n  } catch (gatewayError) {\n    console.error(`POST request failed through API Gateway for ${url}:`, gatewayError);\n\n    // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\n    let directUrl: string | null = null;\n\n    if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {\n      directUrl = `http://localhost:8083${url}`;\n    } else if (url.startsWith('/api/customer-payment')) {\n      directUrl = `http://localhost:8084${url}`;\n    } else if (url.startsWith('/api/customer')) {\n      directUrl = `http://localhost:8081${url}`;\n    } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {\n      directUrl = `http://localhost:8082${url}`;\n    }\n\n    if (directUrl) {\n      try {\n        const directResponse = await axios.post(directUrl, data, {\n          ...config,\n          headers: {\n            ...config?.headers,\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        return directResponse.data;\n      } catch (directError) {\n        // Xử lý lỗi từ direct connection\n        if (axios.isAxiosError(directError) && directError.response?.data) {\n          if (typeof directError.response.data === 'string') {\n            directError.message = directError.response.data;\n          } else if (directError.response.data.message) {\n            directError.message = directError.response.data.message;\n          }\n        }\n        throw directError;\n      }\n    }\n\n    throw gatewayError;\n  }\n};\n\n// Generic PUT request\nexport const put = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n  try {\n    const response: AxiosResponse<T> = await apiClient.put(url, data, config);\n    return response.data;\n  } catch (gatewayError) {\n    console.error(`PUT request failed through API Gateway for ${url}:`, gatewayError);\n\n    // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\n    let directUrl: string | null = null;\n\n    if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {\n      directUrl = `http://localhost:8083${url}`;\n    } else if (url.startsWith('/api/customer-payment')) {\n      directUrl = `http://localhost:8084${url}`;\n    } else if (url.startsWith('/api/customer')) {\n      directUrl = `http://localhost:8081${url}`;\n    } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {\n      directUrl = `http://localhost:8082${url}`;\n    }\n\n    if (directUrl) {\n      const directResponse = await axios.put(directUrl, data, {\n        ...config,\n        headers: {\n          ...config?.headers,\n          'Accept': 'application/json',\n          'Content-Type': 'application/json'\n        }\n      });\n      return directResponse.data;\n    }\n\n    throw gatewayError;\n  }\n};\n\n// Generic DELETE request\nexport const del = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n  try {\n    const response: AxiosResponse<T> = await apiClient.delete(url, config);\n    return response.data;\n  } catch (gatewayError) {\n    console.error(`DELETE request failed through API Gateway for ${url}:`, gatewayError);\n\n    // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\n    let directUrl: string | null = null;\n\n    if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {\n      directUrl = `http://localhost:8083${url}`;\n    } else if (url.startsWith('/api/customer-payment')) {\n      directUrl = `http://localhost:8084${url}`;\n    } else if (url.startsWith('/api/customer')) {\n      directUrl = `http://localhost:8081${url}`;\n    } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {\n      directUrl = `http://localhost:8082${url}`;\n    }\n\n    if (directUrl) {\n      const directResponse = await axios.delete(directUrl, {\n        ...config,\n        headers: {\n          ...config?.headers,\n          'Accept': 'application/json',\n          'Content-Type': 'application/json'\n        }\n      });\n      return directResponse.data;\n    }\n\n    throw gatewayError;\n  }\n};\n\nexport default apiClient;\n"], "mappings": "gKAAA,MAAO,CAAAA,KAAK,KAA4D,OAAO,CAE/E;AACA,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB;AACA,GAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAK,WAAW,EAAIF,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAK,WAAW,CAAE,CACxF;AACA,KAAM,CAAAC,aAAa,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CAC9EC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEL,aAAa,CAAC,CACpD,MAAO,CAAAA,aAAa,CACtB,CAEA;AACA,MAAO,EAAE,CACX,CAAC,CAED;AACA,KAAM,CAAAM,eAAe,CAAG,KAAAA,CAAA,GAA8B,CACpD,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAZ,KAAK,CAACa,GAAG,IAAAC,MAAA,CAAIb,UAAU,CAAC,CAAC,qBAAoB,CAClEc,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE,CACP,QAAQ,CAAE,kBACZ,CACF,CAAC,CAAC,CACF,MAAO,CAAAJ,QAAQ,CAACK,MAAM,GAAK,GAAG,CAChC,CAAE,MAAOC,KAAK,CAAE,CACdT,OAAO,CAACS,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxD,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACA,KAAM,CAAAC,SAAwB,CAAGnB,KAAK,CAACoB,MAAM,CAAC,CAC5CC,OAAO,CAAEpB,UAAU,CAAC,CAAC,CAAE;AACvBe,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClC,QAAQ,CAAE,kBAAkB,CAC5B,kBAAkB,CAAE,gBAAiB;AACvC,CAAC,CACDD,OAAO,CAAE,KAAK,CAAE;AAChBO,eAAe,CAAE,KAAM;AACzB,CAAC,CAAC,CAEF;AACAH,SAAS,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/BC,MAAM,EAAK,KAAAC,cAAA,CACV;AACAlB,OAAO,CAACC,GAAG,iBAAAI,MAAA,EAAAa,cAAA,CAAiBD,MAAM,CAACE,MAAM,UAAAD,cAAA,iBAAbA,cAAA,CAAeE,WAAW,CAAC,CAAC,MAAAf,MAAA,CAAIY,MAAM,CAACI,GAAG,CAAE,CAAC,CACzE,MAAO,CAAAJ,MAAM,CACf,CAAC,CACAR,KAAK,EAAK,CACTT,OAAO,CAACS,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAAC,CACtC,MAAO,CAAAa,OAAO,CAACC,MAAM,CAACd,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACAC,SAAS,CAACI,YAAY,CAACX,QAAQ,CAACa,GAAG,CAChCb,QAAQ,EAAK,CACZ;AACAH,OAAO,CAACC,GAAG,kBAAAI,MAAA,CAAkBF,QAAQ,CAACK,MAAM,MAAAH,MAAA,CAAIF,QAAQ,CAACc,MAAM,CAACI,GAAG,CAAE,CAAC,CAEtE;AACA,GAAI,CACF,GAAI,MAAO,CAAAlB,QAAQ,CAACqB,IAAI,GAAK,QAAQ,EAAIrB,QAAQ,CAACqB,IAAI,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACpEzB,OAAO,CAAC0B,IAAI,CAAC,kDAAkD,CAAEvB,QAAQ,CAACqB,IAAI,CAAC,CAC/ErB,QAAQ,CAACqB,IAAI,CAAGG,IAAI,CAACC,KAAK,CAACzB,QAAQ,CAACqB,IAAI,CAAC,CAC3C,CACF,CAAE,MAAOK,CAAC,CAAE,CACV7B,OAAO,CAACS,KAAK,CAAC,wCAAwC,CAAEoB,CAAC,CAAC,CAC5D,CAEA,MAAO,CAAA1B,QAAQ,CACjB,CAAC,CACAM,KAAK,EAAK,CACT;AACA,GAAIlB,KAAK,CAACuC,YAAY,CAACrB,KAAK,CAAC,CAAE,KAAAsB,eAAA,CAAAC,aAAA,CAAAC,cAAA,CAAAC,gBAAA,CAAAC,gBAAA,CAAAC,gBAAA,CAAAC,gBAAA,CAAAC,gBAAA,CAAAC,gBAAA,CAC7B,KAAM,CAAAC,SAAS,CAAG,CAChBC,OAAO,CAAEhC,KAAK,CAACgC,OAAO,CACtBjC,MAAM,EAAAuB,eAAA,CAAEtB,KAAK,CAACN,QAAQ,UAAA4B,eAAA,iBAAdA,eAAA,CAAgBvB,MAAM,CAC9Ba,GAAG,EAAAW,aAAA,CAAEvB,KAAK,CAACQ,MAAM,UAAAe,aAAA,iBAAZA,aAAA,CAAcX,GAAG,CACtBF,MAAM,EAAAc,cAAA,CAAExB,KAAK,CAACQ,MAAM,UAAAgB,cAAA,iBAAZA,cAAA,CAAcd,MAAM,CAC5BK,IAAI,EAAAU,gBAAA,CAAEzB,KAAK,CAACN,QAAQ,UAAA+B,gBAAA,iBAAdA,gBAAA,CAAgBV,IAAI,CAC1BkB,IAAI,CAAEjC,KAAK,CAACiC,IACd,CAAC,CAED1C,OAAO,CAACS,KAAK,CAAC,YAAY,CAAE+B,SAAS,CAAC,CAEtC;AACA,IAAAL,gBAAA,CAAI1B,KAAK,CAACN,QAAQ,UAAAgC,gBAAA,WAAdA,gBAAA,CAAgBX,IAAI,CAAE,CACxB;AACA,GAAI,MAAO,CAAAf,KAAK,CAACN,QAAQ,CAACqB,IAAI,GAAK,QAAQ,CAAE,CAC3Cf,KAAK,CAACgC,OAAO,CAAGhC,KAAK,CAACN,QAAQ,CAACqB,IAAI,CACrC,CAAC,IAAM,IAAIf,KAAK,CAACN,QAAQ,CAACqB,IAAI,CAACiB,OAAO,CAAE,CACtChC,KAAK,CAACgC,OAAO,CAAGhC,KAAK,CAACN,QAAQ,CAACqB,IAAI,CAACiB,OAAO,CAC7C,CAAC,IAAM,IAAIhC,KAAK,CAACN,QAAQ,CAACqB,IAAI,CAACf,KAAK,CAAE,CACpCA,KAAK,CAACgC,OAAO,CAAGhC,KAAK,CAACN,QAAQ,CAACqB,IAAI,CAACf,KAAK,CAC3C,CACF,CAEA;AACA,GAAIA,KAAK,CAACiC,IAAI,GAAK,cAAc,CAAE,CACjC1C,OAAO,CAACS,KAAK,CAAC,uDAAuD,CAAC,CACtEA,KAAK,CAACgC,OAAO,CAAG,4EAA4E,CAC9F,CAAC,IAAM,IAAIhC,KAAK,CAACgC,OAAO,CAACE,QAAQ,CAAC,eAAe,CAAC,EAAI,CAAClC,KAAK,CAACN,QAAQ,CAAE,CACrEH,OAAO,CAACS,KAAK,CAAC,0EAA0E,CAAC,CAEzF;AACA,GAAIA,KAAK,CAACgC,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,CAAE,CAClC3C,OAAO,CAACS,KAAK,CAAC,0DAA0D,CAAC,CACzEA,KAAK,CAACgC,OAAO,CAAG,4EAA4E,CAC9F,CAAC,IAAM,CACLhC,KAAK,CAACgC,OAAO,CAAG,yFAAyF,CAC3G,CACF,CAAC,IAAM,IAAI,EAAAL,gBAAA,CAAA3B,KAAK,CAACN,QAAQ,UAAAiC,gBAAA,iBAAdA,gBAAA,CAAgB5B,MAAM,IAAK,GAAG,CAAE,KAAAoC,oBAAA,CACzC,GAAI,GAAAA,oBAAA,CAACnC,KAAK,CAACN,QAAQ,CAACqB,IAAI,UAAAoB,oBAAA,WAAnBA,oBAAA,CAAqBH,OAAO,EAAE,CACjChC,KAAK,CAACgC,OAAO,CAAG,oCAAoC,CACtD,CACF,CAAC,IAAM,IAAI,EAAAJ,gBAAA,CAAA5B,KAAK,CAACN,QAAQ,UAAAkC,gBAAA,iBAAdA,gBAAA,CAAgB7B,MAAM,IAAK,GAAG,CAAE,KAAAqC,qBAAA,CACzC,GAAI,GAAAA,qBAAA,CAACpC,KAAK,CAACN,QAAQ,CAACqB,IAAI,UAAAqB,qBAAA,WAAnBA,qBAAA,CAAqBJ,OAAO,EAAE,CACjChC,KAAK,CAACgC,OAAO,CAAG,2CAA2C,CAC7D,CACF,CAAC,IAAM,IAAI,EAAAH,gBAAA,CAAA7B,KAAK,CAACN,QAAQ,UAAAmC,gBAAA,iBAAdA,gBAAA,CAAgB9B,MAAM,IAAK,GAAG,CAAE,KAAAsC,qBAAA,CACzC,GAAI,GAAAA,qBAAA,CAACrC,KAAK,CAACN,QAAQ,CAACqB,IAAI,UAAAsB,qBAAA,WAAnBA,qBAAA,CAAqBL,OAAO,EAAE,CACjChC,KAAK,CAACgC,OAAO,CAAG,6CAA6C,CAC/D,CACF,CAAC,IAAM,IAAI,EAAAF,gBAAA,CAAA9B,KAAK,CAACN,QAAQ,UAAAoC,gBAAA,iBAAdA,gBAAA,CAAgB/B,MAAM,IAAK,GAAG,CAAE,KAAAuC,qBAAA,CACzC;AACA,GAAI,GAAAA,qBAAA,CAACtC,KAAK,CAACN,QAAQ,CAACqB,IAAI,UAAAuB,qBAAA,WAAnBA,qBAAA,CAAqBN,OAAO,EAAE,CACjChC,KAAK,CAACgC,OAAO,CAAG,wDAAwD,CAC1E,CACF,CACF,CAAC,IAAM,CACLzC,OAAO,CAACS,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CAC3C,CACA,MAAO,CAAAa,OAAO,CAACC,MAAM,CAACd,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAL,GAAG,CAAG,KAAAA,CAAUiB,GAAW,CAAEJ,MAA2B,GAAiB,CACpF,GAAI,CACF;AACAjB,OAAO,CAACC,GAAG,2BAAAI,MAAA,CAA2Bb,UAAU,CAAC,CAAC,EAAAa,MAAA,CAAGgB,GAAG,EAAIJ,MAAM,CAAC,CAEnE;AACA,GAAI,CACF,KAAM,CAAAd,QAA0B,CAAG,KAAM,CAAAO,SAAS,CAACN,GAAG,CAACiB,GAAG,CAAEJ,MAAM,CAAC,CACnEjB,OAAO,CAACC,GAAG,mBAAAI,MAAA,CAAmBgB,GAAG,4BAA2BlB,QAAQ,CAACK,MAAM,CAAC,CAC5E,MAAO,CAAAL,QAAQ,CAACqB,IAAI,CACtB,CAAE,MAAOwB,YAAY,CAAE,CACrBhD,OAAO,CAACS,KAAK,+CAAAJ,MAAA,CAA+CgB,GAAG,MAAK2B,YAAY,CAAC,CAEjF;AACA,GAAI,CAAAC,SAAwB,CAAG,IAAI,CAEnC,GAAI5B,GAAG,CAAC6B,UAAU,CAAC,0BAA0B,CAAC,CAAE,CAC9CD,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CACzCrB,OAAO,CAACC,GAAG,gEAAAI,MAAA,CAAgEgB,GAAG,CAAE,CAAC,CACnF,CAAC,IAAM,IAAIA,GAAG,CAAC6B,UAAU,CAAC,wBAAwB,CAAC,EAAI7B,GAAG,CAAC6B,UAAU,CAAC,iBAAiB,CAAC,EAAI7B,GAAG,CAAC6B,UAAU,CAAC,iBAAiB,CAAC,CAAE,CAC7HD,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CACzCrB,OAAO,CAACC,GAAG,8DAAAI,MAAA,CAA8DgB,GAAG,CAAE,CAAC,CACjF,CAAC,IAAM,IAAIA,GAAG,CAAC6B,UAAU,CAAC,uBAAuB,CAAC,CAAE,CAClDD,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CACzCrB,OAAO,CAACC,GAAG,6DAAAI,MAAA,CAA6DgB,GAAG,CAAE,CAAC,CAChF,CAAC,IAAM,IAAIA,GAAG,CAAC6B,UAAU,CAAC,eAAe,CAAC,CAAE,CAC1CD,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CACzCrB,OAAO,CAACC,GAAG,qDAAAI,MAAA,CAAqDgB,GAAG,CAAE,CAAC,CACxE,CAAC,IAAM,IAAIA,GAAG,CAAC6B,UAAU,CAAC,UAAU,CAAC,EAAI7B,GAAG,CAAC6B,UAAU,CAAC,mBAAmB,CAAC,CAAE,CAC5ED,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CACzCrB,OAAO,CAACC,GAAG,gDAAAI,MAAA,CAAgDgB,GAAG,CAAE,CAAC,CACnE,CAEA,GAAI4B,SAAS,CAAE,CACbjD,OAAO,CAACC,GAAG,gBAAAI,MAAA,CAAgB4C,SAAS,CAAE,CAAC,CAEvC;AACA,KAAM,CAAAE,cAAc,CAAG,KAAM,CAAA5D,KAAK,CAACa,GAAG,CAAC6C,SAAS,CAAAG,aAAA,CAAAA,aAAA,IAC3CnC,MAAM,MACTV,OAAO,CAAA6C,aAAA,CAAAA,aAAA,IACFnC,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEV,OAAO,MAClB,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAAkB,EACnC,EACF,CAAC,CAEFP,OAAO,CAACC,GAAG,0BAAAI,MAAA,CAA0B4C,SAAS,4BAA2BE,cAAc,CAAC3C,MAAM,CAAC,CAC/F,MAAO,CAAA2C,cAAc,CAAC3B,IAAI,CAC5B,CAEA;AACA,KAAM,CAAAwB,YAAY,CACpB,CACF,CAAE,MAAOvC,KAAK,CAAE,CACdT,OAAO,CAACS,KAAK,2BAAAJ,MAAA,CAA2BgB,GAAG,MAAKZ,KAAK,CAAC,CACtD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAA4C,IAAI,CAAG,KAAAA,CAAUhC,GAAW,CAAEG,IAAU,CAAEP,MAA2B,GAAiB,CACjG,GAAI,CACF,KAAM,CAAAd,QAA0B,CAAG,KAAM,CAAAO,SAAS,CAAC2C,IAAI,CAAChC,GAAG,CAAEG,IAAI,CAAEP,MAAM,CAAC,CAC1E,MAAO,CAAAd,QAAQ,CAACqB,IAAI,CACtB,CAAE,MAAOwB,YAAY,CAAE,CACrBhD,OAAO,CAACS,KAAK,gDAAAJ,MAAA,CAAgDgB,GAAG,MAAK2B,YAAY,CAAC,CAElF;AACA,GAAI,CAAAC,SAAwB,CAAG,IAAI,CAEnC,GAAI5B,GAAG,CAAC6B,UAAU,CAAC,wBAAwB,CAAC,EAAI7B,GAAG,CAAC6B,UAAU,CAAC,iBAAiB,CAAC,EAAI7B,GAAG,CAAC6B,UAAU,CAAC,iBAAiB,CAAC,CAAE,CACtHD,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CAC3C,CAAC,IAAM,IAAIA,GAAG,CAAC6B,UAAU,CAAC,uBAAuB,CAAC,CAAE,CAClDD,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CAC3C,CAAC,IAAM,IAAIA,GAAG,CAAC6B,UAAU,CAAC,eAAe,CAAC,CAAE,CAC1CD,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CAC3C,CAAC,IAAM,IAAIA,GAAG,CAAC6B,UAAU,CAAC,UAAU,CAAC,EAAI7B,GAAG,CAAC6B,UAAU,CAAC,mBAAmB,CAAC,CAAE,CAC5ED,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CAC3C,CAEA,GAAI4B,SAAS,CAAE,CACb,GAAI,CACF,KAAM,CAAAE,cAAc,CAAG,KAAM,CAAA5D,KAAK,CAAC8D,IAAI,CAACJ,SAAS,CAAEzB,IAAI,CAAA4B,aAAA,CAAAA,aAAA,IAClDnC,MAAM,MACTV,OAAO,CAAA6C,aAAA,CAAAA,aAAA,IACFnC,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEV,OAAO,MAClB,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAAkB,EACnC,EACF,CAAC,CACF,MAAO,CAAA4C,cAAc,CAAC3B,IAAI,CAC5B,CAAE,MAAO8B,WAAW,CAAE,KAAAC,qBAAA,CACpB;AACA,GAAIhE,KAAK,CAACuC,YAAY,CAACwB,WAAW,CAAC,GAAAC,qBAAA,CAAID,WAAW,CAACnD,QAAQ,UAAAoD,qBAAA,WAApBA,qBAAA,CAAsB/B,IAAI,CAAE,CACjE,GAAI,MAAO,CAAA8B,WAAW,CAACnD,QAAQ,CAACqB,IAAI,GAAK,QAAQ,CAAE,CACjD8B,WAAW,CAACb,OAAO,CAAGa,WAAW,CAACnD,QAAQ,CAACqB,IAAI,CACjD,CAAC,IAAM,IAAI8B,WAAW,CAACnD,QAAQ,CAACqB,IAAI,CAACiB,OAAO,CAAE,CAC5Ca,WAAW,CAACb,OAAO,CAAGa,WAAW,CAACnD,QAAQ,CAACqB,IAAI,CAACiB,OAAO,CACzD,CACF,CACA,KAAM,CAAAa,WAAW,CACnB,CACF,CAEA,KAAM,CAAAN,YAAY,CACpB,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAQ,GAAG,CAAG,KAAAA,CAAUnC,GAAW,CAAEG,IAAU,CAAEP,MAA2B,GAAiB,CAChG,GAAI,CACF,KAAM,CAAAd,QAA0B,CAAG,KAAM,CAAAO,SAAS,CAAC8C,GAAG,CAACnC,GAAG,CAAEG,IAAI,CAAEP,MAAM,CAAC,CACzE,MAAO,CAAAd,QAAQ,CAACqB,IAAI,CACtB,CAAE,MAAOwB,YAAY,CAAE,CACrBhD,OAAO,CAACS,KAAK,+CAAAJ,MAAA,CAA+CgB,GAAG,MAAK2B,YAAY,CAAC,CAEjF;AACA,GAAI,CAAAC,SAAwB,CAAG,IAAI,CAEnC,GAAI5B,GAAG,CAAC6B,UAAU,CAAC,wBAAwB,CAAC,EAAI7B,GAAG,CAAC6B,UAAU,CAAC,iBAAiB,CAAC,EAAI7B,GAAG,CAAC6B,UAAU,CAAC,iBAAiB,CAAC,CAAE,CACtHD,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CAC3C,CAAC,IAAM,IAAIA,GAAG,CAAC6B,UAAU,CAAC,uBAAuB,CAAC,CAAE,CAClDD,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CAC3C,CAAC,IAAM,IAAIA,GAAG,CAAC6B,UAAU,CAAC,eAAe,CAAC,CAAE,CAC1CD,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CAC3C,CAAC,IAAM,IAAIA,GAAG,CAAC6B,UAAU,CAAC,UAAU,CAAC,EAAI7B,GAAG,CAAC6B,UAAU,CAAC,mBAAmB,CAAC,CAAE,CAC5ED,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CAC3C,CAEA,GAAI4B,SAAS,CAAE,CACb,KAAM,CAAAE,cAAc,CAAG,KAAM,CAAA5D,KAAK,CAACiE,GAAG,CAACP,SAAS,CAAEzB,IAAI,CAAA4B,aAAA,CAAAA,aAAA,IACjDnC,MAAM,MACTV,OAAO,CAAA6C,aAAA,CAAAA,aAAA,IACFnC,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEV,OAAO,MAClB,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAAkB,EACnC,EACF,CAAC,CACF,MAAO,CAAA4C,cAAc,CAAC3B,IAAI,CAC5B,CAEA,KAAM,CAAAwB,YAAY,CACpB,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAS,GAAG,CAAG,KAAAA,CAAUpC,GAAW,CAAEJ,MAA2B,GAAiB,CACpF,GAAI,CACF,KAAM,CAAAd,QAA0B,CAAG,KAAM,CAAAO,SAAS,CAACgD,MAAM,CAACrC,GAAG,CAAEJ,MAAM,CAAC,CACtE,MAAO,CAAAd,QAAQ,CAACqB,IAAI,CACtB,CAAE,MAAOwB,YAAY,CAAE,CACrBhD,OAAO,CAACS,KAAK,kDAAAJ,MAAA,CAAkDgB,GAAG,MAAK2B,YAAY,CAAC,CAEpF;AACA,GAAI,CAAAC,SAAwB,CAAG,IAAI,CAEnC,GAAI5B,GAAG,CAAC6B,UAAU,CAAC,wBAAwB,CAAC,EAAI7B,GAAG,CAAC6B,UAAU,CAAC,iBAAiB,CAAC,EAAI7B,GAAG,CAAC6B,UAAU,CAAC,iBAAiB,CAAC,CAAE,CACtHD,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CAC3C,CAAC,IAAM,IAAIA,GAAG,CAAC6B,UAAU,CAAC,uBAAuB,CAAC,CAAE,CAClDD,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CAC3C,CAAC,IAAM,IAAIA,GAAG,CAAC6B,UAAU,CAAC,eAAe,CAAC,CAAE,CAC1CD,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CAC3C,CAAC,IAAM,IAAIA,GAAG,CAAC6B,UAAU,CAAC,UAAU,CAAC,EAAI7B,GAAG,CAAC6B,UAAU,CAAC,mBAAmB,CAAC,CAAE,CAC5ED,SAAS,yBAAA5C,MAAA,CAA2BgB,GAAG,CAAE,CAC3C,CAEA,GAAI4B,SAAS,CAAE,CACb,KAAM,CAAAE,cAAc,CAAG,KAAM,CAAA5D,KAAK,CAACmE,MAAM,CAACT,SAAS,CAAAG,aAAA,CAAAA,aAAA,IAC9CnC,MAAM,MACTV,OAAO,CAAA6C,aAAA,CAAAA,aAAA,IACFnC,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEV,OAAO,MAClB,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAAkB,EACnC,EACF,CAAC,CACF,MAAO,CAAA4C,cAAc,CAAC3B,IAAI,CAC5B,CAEA,KAAM,CAAAwB,YAAY,CACpB,CACF,CAAC,CAED,cAAe,CAAAtC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}