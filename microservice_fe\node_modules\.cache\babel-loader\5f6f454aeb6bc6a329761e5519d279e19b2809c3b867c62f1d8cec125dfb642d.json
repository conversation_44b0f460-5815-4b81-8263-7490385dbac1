{"ast": null, "code": "import _objectSpread from\"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useNavigate}from'react-router-dom';import{Box}from'@mui/material';import{CustomerContractForm}from'../components/contract';import{contractService}from'../services/contract/contractService';import{LoadingSpinner,ErrorAlert,SuccessAlert}from'../components/common';import{calculateContractAmount}from'../utils/contractCalculationUtils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CreateContractPage=()=>{const navigate=useNavigate();const[contract,setContract]=useState({customerId:0,startingDate:'',endingDate:'',totalAmount:0,address:'',description:'',jobDetails:[],status:0// Pending\n});const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[success,setSuccess]=useState(null);const handleContractChange=updatedContract=>{setContract(updatedContract);};const validateContract=()=>{if(!contract.customerId||contract.customerId===0){setError('Vui lòng chọn khách hàng');return false;}// Contract dates are auto-calculated from job details, no need to validate manually\nif(!contract.address){setError('Vui lòng nhập địa chỉ hợp đồng');return false;}// Total amount is auto-calculated, no need to validate manually\nif(!contract.jobDetails||contract.jobDetails.length===0){setError('Vui lòng thêm ít nhất một chi tiết công việc');return false;}// Validate each job detail\nfor(const jobDetail of contract.jobDetails){if(!jobDetail.jobCategoryId||jobDetail.jobCategoryId===0){setError('Vui lòng chọn loại công việc cho tất cả chi tiết công việc');return false;}if(!jobDetail.startDate){setError('Vui lòng nhập ngày bắt đầu cho tất cả chi tiết công việc');return false;}if(!jobDetail.endDate){setError('Vui lòng nhập ngày kết thúc cho tất cả chi tiết công việc');return false;}if(!jobDetail.workLocation){setError('Vui lòng nhập địa điểm làm việc cho tất cả chi tiết công việc');return false;}if(!jobDetail.workShifts||jobDetail.workShifts.length===0){setError('Vui lòng thêm ít nhất một ca làm việc cho mỗi chi tiết công việc');return false;}// Validate each work shift\nfor(const workShift of jobDetail.workShifts){if(!workShift.startTime){setError('Vui lòng nhập giờ bắt đầu cho tất cả ca làm việc');return false;}if(!workShift.endTime){setError('Vui lòng nhập giờ kết thúc cho tất cả ca làm việc');return false;}if(!workShift.numberOfWorkers||workShift.numberOfWorkers<=0){setError('Vui lòng nhập số lượng người lao động hợp lệ cho tất cả ca làm việc');return false;}if(workShift.salary===undefined||workShift.salary<0){setError('Vui lòng nhập mức lương hợp lệ cho tất cả ca làm việc');return false;}if(!workShift.workingDays){setError('Vui lòng chọn ngày làm việc cho tất cả ca làm việc');return false;}}}return true;};const handleSubmit=async()=>{// Prevent double submission\nif(loading){return;}setError(null);if(!validateContract()){return;}// Ensure total amount is calculated before submitting\nconst calculation=calculateContractAmount(contract);const contractToSubmit=_objectSpread(_objectSpread({},contract),{},{totalAmount:calculation.totalAmount});setLoading(true);try{const createdContract=await contractService.createContract(contractToSubmit);setSuccess('Hợp đồng đã được tạo thành công!');// Redirect to the contract details page after a short delay\nsetTimeout(()=>{navigate(\"/contracts/\".concat(createdContract.id));},2000);}catch(err){setError(err.message||'Đã xảy ra lỗi khi tạo hợp đồng');}finally{setLoading(false);}};if(loading){return/*#__PURE__*/_jsx(LoadingSpinner,{fullScreen:true});}return/*#__PURE__*/_jsxs(Box,{children:[error&&/*#__PURE__*/_jsx(ErrorAlert,{message:error}),success&&/*#__PURE__*/_jsx(SuccessAlert,{message:success}),/*#__PURE__*/_jsx(CustomerContractForm,{contract:contract,onChange:handleContractChange,onSubmit:handleSubmit,loading:loading})]});};export default CreateContractPage;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Box", "CustomerContractForm", "contractService", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "calculateContractAmount", "jsx", "_jsx", "jsxs", "_jsxs", "CreateContractPage", "navigate", "contract", "setContract", "customerId", "startingDate", "endingDate", "totalAmount", "address", "description", "jobDetails", "status", "loading", "setLoading", "error", "setError", "success", "setSuccess", "handleContractChange", "updatedContract", "validateContract", "length", "jobDetail", "jobCategoryId", "startDate", "endDate", "workLocation", "workShifts", "workShift", "startTime", "endTime", "numberOfWorkers", "salary", "undefined", "workingDays", "handleSubmit", "calculation", "contractToSubmit", "_objectSpread", "createdContract", "createContract", "setTimeout", "concat", "id", "err", "message", "fullScreen", "children", "onChange", "onSubmit"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/pages/CreateContractPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Box } from '@mui/material';\nimport { CustomerContractForm } from '../components/contract';\nimport { CustomerContract } from '../models';\nimport { contractService } from '../services/contract/contractService';\nimport { LoadingSpinner, ErrorAlert, SuccessAlert } from '../components/common';\nimport { calculateContractAmount } from '../utils/contractCalculationUtils';\n\nconst CreateContractPage: React.FC = () => {\n  const navigate = useNavigate();\n  const [contract, setContract] = useState<Partial<CustomerContract>>({\n    customerId: 0,\n    startingDate: '',\n    endingDate: '',\n    totalAmount: 0,\n    address: '',\n    description: '',\n    jobDetails: [],\n    status: 0 // Pending\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  const handleContractChange = (updatedContract: Partial<CustomerContract>) => {\n    setContract(updatedContract);\n  };\n\n  const validateContract = (): boolean => {\n    if (!contract.customerId || contract.customerId === 0) {\n      setError('Vui lòng chọn khách hàng');\n      return false;\n    }\n\n    // Contract dates are auto-calculated from job details, no need to validate manually\n\n    if (!contract.address) {\n      setError('Vui lòng nhập địa chỉ hợp đồng');\n      return false;\n    }\n\n    // Total amount is auto-calculated, no need to validate manually\n\n    if (!contract.jobDetails || contract.jobDetails.length === 0) {\n      setError('Vui lòng thêm ít nhất một chi tiết công việc');\n      return false;\n    }\n\n    // Validate each job detail\n    for (const jobDetail of contract.jobDetails) {\n      if (!jobDetail.jobCategoryId || jobDetail.jobCategoryId === 0) {\n        setError('Vui lòng chọn loại công việc cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.startDate) {\n        setError('Vui lòng nhập ngày bắt đầu cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.endDate) {\n        setError('Vui lòng nhập ngày kết thúc cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.workLocation) {\n        setError('Vui lòng nhập địa điểm làm việc cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) {\n        setError('Vui lòng thêm ít nhất một ca làm việc cho mỗi chi tiết công việc');\n        return false;\n      }\n\n      // Validate each work shift\n      for (const workShift of jobDetail.workShifts) {\n        if (!workShift.startTime) {\n          setError('Vui lòng nhập giờ bắt đầu cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (!workShift.endTime) {\n          setError('Vui lòng nhập giờ kết thúc cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (!workShift.numberOfWorkers || workShift.numberOfWorkers <= 0) {\n          setError('Vui lòng nhập số lượng người lao động hợp lệ cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (workShift.salary === undefined || workShift.salary < 0) {\n          setError('Vui lòng nhập mức lương hợp lệ cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (!workShift.workingDays) {\n          setError('Vui lòng chọn ngày làm việc cho tất cả ca làm việc');\n          return false;\n        }\n      }\n    }\n\n    return true;\n  };\n\n  const handleSubmit = async () => {\n    // Prevent double submission\n    if (loading) {\n      return;\n    }\n\n    setError(null);\n\n    if (!validateContract()) {\n      return;\n    }\n\n    // Ensure total amount is calculated before submitting\n    const calculation = calculateContractAmount(contract);\n    const contractToSubmit = {\n      ...contract,\n      totalAmount: calculation.totalAmount\n    };\n\n    setLoading(true);\n\n    try {\n      const createdContract = await contractService.createContract(contractToSubmit as CustomerContract);\n      setSuccess('Hợp đồng đã được tạo thành công!');\n\n      // Redirect to the contract details page after a short delay\n      setTimeout(() => {\n        navigate(`/contracts/${createdContract.id}`);\n      }, 2000);\n    } catch (err: any) {\n      setError(err.message || 'Đã xảy ra lỗi khi tạo hợp đồng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return <LoadingSpinner fullScreen />;\n  }\n\n  return (\n    <Box>\n      {error && <ErrorAlert message={error} />}\n      {success && <SuccessAlert message={success} />}\n\n      <CustomerContractForm\n        contract={contract}\n        onChange={handleContractChange}\n        onSubmit={handleSubmit}\n        loading={loading}\n      />\n    </Box>\n  );\n};\n\nexport default CreateContractPage;\n"], "mappings": "gKAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,GAAG,KAAQ,eAAe,CACnC,OAASC,oBAAoB,KAAQ,wBAAwB,CAE7D,OAASC,eAAe,KAAQ,sCAAsC,CACtE,OAASC,cAAc,CAAEC,UAAU,CAAEC,YAAY,KAAQ,sBAAsB,CAC/E,OAASC,uBAAuB,KAAQ,mCAAmC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5E,KAAM,CAAAC,kBAA4B,CAAGA,CAAA,GAAM,CACzC,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACc,QAAQ,CAAEC,WAAW,CAAC,CAAGhB,QAAQ,CAA4B,CAClEiB,UAAU,CAAE,CAAC,CACbC,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAE,EAAE,CACdC,WAAW,CAAE,CAAC,CACdC,OAAO,CAAE,EAAE,CACXC,WAAW,CAAE,EAAE,CACfC,UAAU,CAAE,EAAE,CACdC,MAAM,CAAE,CAAE;AACZ,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC2B,KAAK,CAAEC,QAAQ,CAAC,CAAG5B,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAAC6B,OAAO,CAAEC,UAAU,CAAC,CAAG9B,QAAQ,CAAgB,IAAI,CAAC,CAE3D,KAAM,CAAA+B,oBAAoB,CAAIC,eAA0C,EAAK,CAC3EhB,WAAW,CAACgB,eAAe,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAe,CACtC,GAAI,CAAClB,QAAQ,CAACE,UAAU,EAAIF,QAAQ,CAACE,UAAU,GAAK,CAAC,CAAE,CACrDW,QAAQ,CAAC,0BAA0B,CAAC,CACpC,MAAO,MAAK,CACd,CAEA;AAEA,GAAI,CAACb,QAAQ,CAACM,OAAO,CAAE,CACrBO,QAAQ,CAAC,gCAAgC,CAAC,CAC1C,MAAO,MAAK,CACd,CAEA;AAEA,GAAI,CAACb,QAAQ,CAACQ,UAAU,EAAIR,QAAQ,CAACQ,UAAU,CAACW,MAAM,GAAK,CAAC,CAAE,CAC5DN,QAAQ,CAAC,8CAA8C,CAAC,CACxD,MAAO,MAAK,CACd,CAEA;AACA,IAAK,KAAM,CAAAO,SAAS,GAAI,CAAApB,QAAQ,CAACQ,UAAU,CAAE,CAC3C,GAAI,CAACY,SAAS,CAACC,aAAa,EAAID,SAAS,CAACC,aAAa,GAAK,CAAC,CAAE,CAC7DR,QAAQ,CAAC,4DAA4D,CAAC,CACtE,MAAO,MAAK,CACd,CAEA,GAAI,CAACO,SAAS,CAACE,SAAS,CAAE,CACxBT,QAAQ,CAAC,0DAA0D,CAAC,CACpE,MAAO,MAAK,CACd,CAEA,GAAI,CAACO,SAAS,CAACG,OAAO,CAAE,CACtBV,QAAQ,CAAC,2DAA2D,CAAC,CACrE,MAAO,MAAK,CACd,CAEA,GAAI,CAACO,SAAS,CAACI,YAAY,CAAE,CAC3BX,QAAQ,CAAC,+DAA+D,CAAC,CACzE,MAAO,MAAK,CACd,CAEA,GAAI,CAACO,SAAS,CAACK,UAAU,EAAIL,SAAS,CAACK,UAAU,CAACN,MAAM,GAAK,CAAC,CAAE,CAC9DN,QAAQ,CAAC,kEAAkE,CAAC,CAC5E,MAAO,MAAK,CACd,CAEA;AACA,IAAK,KAAM,CAAAa,SAAS,GAAI,CAAAN,SAAS,CAACK,UAAU,CAAE,CAC5C,GAAI,CAACC,SAAS,CAACC,SAAS,CAAE,CACxBd,QAAQ,CAAC,kDAAkD,CAAC,CAC5D,MAAO,MAAK,CACd,CAEA,GAAI,CAACa,SAAS,CAACE,OAAO,CAAE,CACtBf,QAAQ,CAAC,mDAAmD,CAAC,CAC7D,MAAO,MAAK,CACd,CAEA,GAAI,CAACa,SAAS,CAACG,eAAe,EAAIH,SAAS,CAACG,eAAe,EAAI,CAAC,CAAE,CAChEhB,QAAQ,CAAC,qEAAqE,CAAC,CAC/E,MAAO,MAAK,CACd,CAEA,GAAIa,SAAS,CAACI,MAAM,GAAKC,SAAS,EAAIL,SAAS,CAACI,MAAM,CAAG,CAAC,CAAE,CAC1DjB,QAAQ,CAAC,uDAAuD,CAAC,CACjE,MAAO,MAAK,CACd,CAEA,GAAI,CAACa,SAAS,CAACM,WAAW,CAAE,CAC1BnB,QAAQ,CAAC,oDAAoD,CAAC,CAC9D,MAAO,MAAK,CACd,CACF,CACF,CAEA,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAoB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B;AACA,GAAIvB,OAAO,CAAE,CACX,OACF,CAEAG,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CAACK,gBAAgB,CAAC,CAAC,CAAE,CACvB,OACF,CAEA;AACA,KAAM,CAAAgB,WAAW,CAAGzC,uBAAuB,CAACO,QAAQ,CAAC,CACrD,KAAM,CAAAmC,gBAAgB,CAAAC,aAAA,CAAAA,aAAA,IACjBpC,QAAQ,MACXK,WAAW,CAAE6B,WAAW,CAAC7B,WAAW,EACrC,CAEDM,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF,KAAM,CAAA0B,eAAe,CAAG,KAAM,CAAAhD,eAAe,CAACiD,cAAc,CAACH,gBAAoC,CAAC,CAClGpB,UAAU,CAAC,kCAAkC,CAAC,CAE9C;AACAwB,UAAU,CAAC,IAAM,CACfxC,QAAQ,eAAAyC,MAAA,CAAeH,eAAe,CAACI,EAAE,CAAE,CAAC,CAC9C,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOC,GAAQ,CAAE,CACjB7B,QAAQ,CAAC6B,GAAG,CAACC,OAAO,EAAI,gCAAgC,CAAC,CAC3D,CAAC,OAAS,CACRhC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAID,OAAO,CAAE,CACX,mBAAOf,IAAA,CAACL,cAAc,EAACsD,UAAU,MAAE,CAAC,CACtC,CAEA,mBACE/C,KAAA,CAACV,GAAG,EAAA0D,QAAA,EACDjC,KAAK,eAAIjB,IAAA,CAACJ,UAAU,EAACoD,OAAO,CAAE/B,KAAM,CAAE,CAAC,CACvCE,OAAO,eAAInB,IAAA,CAACH,YAAY,EAACmD,OAAO,CAAE7B,OAAQ,CAAE,CAAC,cAE9CnB,IAAA,CAACP,oBAAoB,EACnBY,QAAQ,CAAEA,QAAS,CACnB8C,QAAQ,CAAE9B,oBAAqB,CAC/B+B,QAAQ,CAAEd,YAAa,CACvBvB,OAAO,CAAEA,OAAQ,CAClB,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAZ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}